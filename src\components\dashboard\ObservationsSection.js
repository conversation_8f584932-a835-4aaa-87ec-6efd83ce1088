import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const ObservationsSection = ({ observations, handleChange }) => {
    return (
        <div className="grid-x grid-padding-x ">
            <div className="medium-12 cell">
                <h4>
                    <FontAwesomeIcon icon="pen-alt" />
                    Observations:
                </h4>
                <p>Do you have any observations or special requests?</p>
                <textarea
                    id="observations"
                    value={observations}
                    name="observations"
                    onChange={handleChange}
                    type="text"
                    placeholder="type observations"
                    rows="4">
                </textarea>
            </div>
        </div>
    );
};

export default ObservationsSection;
