import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const ObservationsSection = ({ observations, handleChange, onValidationChange }) => {
    const [hasStoreRequest, setHasStoreRequest] = useState(false);

    // Función para detectar solicitudes de parada en tienda
    const detectStoreRequest = (text) => {
        if (!text) return false;

        const lowerText = text.toLowerCase();

        // Palabras que indican acción de parada/visita
        const stopWords = [
            'stop', 'parada', 'visit', 'visita', 'go to', 'ir a', 'break', 'descanso',
            'detour', 'desvío', 'quick stop', 'brief stop', 'pickup', 'drop off',
            'swing by', 'pass by', 'pasar por', 'hacer parada', 'detenerse'
        ];

        // Palabras que indican tiendas/mercados (incluyendo variaciones)
        const storeWords = [
            // Términos generales
            'store', 'market', 'supermarket', 'super', 'grocery', 'shop', 'shopping',
            'tienda', 'mercado', 'supermercado', 'compras', 'comprar', 'comercial', 'plaza',
            'mall', 'centro comercial', 'shopping center', 'retail',

            // Cadenas específicas de EE.UU.
            'walmart', 'wal-mart', 'costco', 'sams', "sam's", 'target', 'publix', 'kroger',
            'whole foods', 'trader joe', 'trader joes', 'cvs', 'walgreens', 'rite aid',
            'home depot', 'lowes', "lowe's", 'best buy', 'staples', 'office depot',
            'dollar tree', 'dollar general', 'family dollar', 'big lots', 'tj maxx',
            'marshalls', 'ross', 'nordstrom', 'macys', "macy's", 'jcpenney',

            // Farmacias y servicios
            'pharmacy', 'farmacia', 'drugstore', 'gas station', 'gasolinera',
            'convenience store', 'corner store', '7-eleven', 'circle k', 'wawa',

            // Términos en español
            'autoservicio', 'minisuper', 'abarrotes', 'despensa', 'bodega',
            'centro de compras', 'almacén', 'hipermercado'
        ];

        // Palabras que indican tiempo
        const timeWords = [
            'minute', 'min', 'minuto', 'minutos', 'time', 'tiempo', 'hour', 'hora', 'horas',
            'quick', 'rápido', 'fast', 'brief', 'breve', 'short', 'corto', 'long', 'largo',
            'while', 'rato', 'moment', 'momento', 'second', 'segundo', 'segundos'
        ];

        // Verificar si contiene palabras de parada/visita
        const hasStopWord = stopWords.some(word => lowerText.includes(word));

        // Verificar si contiene palabras de tienda
        const hasStoreWord = storeWords.some(word => lowerText.includes(word));

        // Verificar si contiene palabras de tiempo
        const hasTimeWord = timeWords.some(word => lowerText.includes(word));

        // Detectar patrones problemáticos:
        // 1. Parada + Tienda (ej: "stop at store", "parada en tienda")
        const hasStopAndStore = hasStopWord && hasStoreWord;

        // 2. Tiempo + Tienda (ej: "30 minutes at walmart", "tiempo en mercado")
        const hasTimeAndStore = hasTimeWord && hasStoreWord;

        // 3. Solo mencionar tiendas específicas con contexto de solicitud
        const specificStores = [
            'walmart', 'wal-mart', 'costco', 'target', 'publix', 'kroger', 'whole foods',
            'trader joe', 'cvs', 'walgreens', 'home depot', 'lowes', 'best buy',
            '7-eleven', 'circle k', 'dollar tree', 'dollar general'
        ];
        const mentionsSpecificStores = specificStores.some(store => lowerText.includes(store));

        return hasStopAndStore || hasTimeAndStore || mentionsSpecificStores;
    };

    // Validar el texto cada vez que cambie
    useEffect(() => {
        const isInvalid = detectStoreRequest(observations);
        setHasStoreRequest(isInvalid);

        // Notificar al componente padre sobre el estado de validación
        if (onValidationChange) {
            onValidationChange(!isInvalid);
        }
    }, [observations, onValidationChange]);

    const handleTextChange = (e) => {
        handleChange(e);
    };

    return (
        <div className="grid-x grid-padding-x ">
            <div className="medium-12 cell">
                <h4>
                    <FontAwesomeIcon icon="pen-alt" />
                    Observations:
                </h4>
                <p>Do you have any observations or special requests?</p>

                {/* Mensaje de advertencia si se detecta solicitud de parada */}
                {hasStoreRequest && (
                    <div className="alert callout warning" style={{ marginBottom: '10px' }}>
                        <p style={{ margin: 0, fontSize: '14px' }}>
                            <FontAwesomeIcon icon="exclamation-triangle" style={{ marginRight: '8px' }} />
                            <strong>Important:</strong> Store/Market stops are a paid service. Please use the "Extra Service" section above to add this service with its corresponding fee.
                        </p>
                    </div>
                )}

                <textarea
                    id="observations"
                    value={observations}
                    name="observations"
                    onChange={handleTextChange}
                    type="text"
                    placeholder="type observations (store stops are not allowed here - use Extra Service section)"
                    rows="4"
                    style={{
                        borderColor: hasStoreRequest ? '#cc4b37' : '',
                        borderWidth: hasStoreRequest ? '2px' : ''
                    }}>
                </textarea>

                {hasStoreRequest && (
                    <small style={{ color: '#cc4b37', fontSize: '12px', marginTop: '5px', display: 'block' }}>
                        Please remove store/market stop requests from observations and use the Extra Service section instead.
                    </small>
                )}
            </div>
        </div>
    );
};

export default ObservationsSection;
