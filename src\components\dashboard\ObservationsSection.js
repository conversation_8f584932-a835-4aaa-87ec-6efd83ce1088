import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const ObservationsSection = ({ observations, handleChange, onValidationChange }) => {
    const [hasStoreRequest, setHasStoreRequest] = useState(false);

    // Función para detectar solicitudes de parada en tienda
    const detectStoreRequest = (text) => {
        if (!text) return false;

        const lowerText = text.toLowerCase();

        // Buscar combinaciones que indiquen solicitud de parada
        const hasStopAndStore = (lowerText.includes('stop') || lowerText.includes('parada')) &&
                               (lowerText.includes('store') || lowerText.includes('market') ||
                                lowerText.includes('walmart') || lowerText.includes('tienda') ||
                                lowerText.includes('mercado') || lowerText.includes('supermercado'));

        const hasTimeAndStore = (lowerText.includes('minute') || lowerText.includes('min') ||
                                lowerText.includes('minuto') || lowerText.includes('time')) &&
                               (lowerText.includes('store') || lowerText.includes('market') ||
                                lowerText.includes('walmart') || lowerText.includes('tienda') ||
                                lowerText.includes('mercado') || lowerText.includes('supermercado'));

        return hasStopAndStore || hasTimeAndStore;
    };

    // Validar el texto cada vez que cambie
    useEffect(() => {
        const isInvalid = detectStoreRequest(observations);
        setHasStoreRequest(isInvalid);

        // Notificar al componente padre sobre el estado de validación
        if (onValidationChange) {
            onValidationChange(!isInvalid);
        }
    }, [observations, onValidationChange]);

    const handleTextChange = (e) => {
        handleChange(e);
    };

    return (
        <div className="grid-x grid-padding-x ">
            <div className="medium-12 cell">
                <h4>
                    <FontAwesomeIcon icon="pen-alt" />
                    Observations:
                </h4>
                <p>Do you have any observations or special requests?</p>

                {/* Mensaje de advertencia si se detecta solicitud de parada */}
                {hasStoreRequest && (
                    <div className="alert callout warning" style={{ marginBottom: '10px' }}>
                        <p style={{ margin: 0, fontSize: '14px' }}>
                            <FontAwesomeIcon icon="exclamation-triangle" style={{ marginRight: '8px' }} />
                            <strong>Important:</strong> Store/Market stops are a paid service. Please use the "Extra Service" section above to add this service with its corresponding fee.
                        </p>
                    </div>
                )}

                <textarea
                    id="observations"
                    value={observations}
                    name="observations"
                    onChange={handleTextChange}
                    type="text"
                    placeholder="type observations (store stops are not allowed here - use Extra Service section)"
                    rows="4"
                    style={{
                        borderColor: hasStoreRequest ? '#cc4b37' : '',
                        borderWidth: hasStoreRequest ? '2px' : ''
                    }}>
                </textarea>

                {hasStoreRequest && (
                    <small style={{ color: '#cc4b37', fontSize: '12px', marginTop: '5px', display: 'block' }}>
                        Please remove store/market stop requests from observations and use the Extra Service section instead.
                    </small>
                )}
            </div>
        </div>
    );
};

export default ObservationsSection;
