import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const ObservationsSection = ({ observations, handleChange, onValidationChange }) => {
    const [hasStoreRequest, setHasStoreRequest] = useState(false);

    // Función para detectar solicitudes de parada en tienda
    const detectStoreRequest = (text) => {
        if (!text) return false;

        const lowerText = text.toLowerCase();

        // Palabras que indican acción de parada/visita
        const stopWords = [
            'stop', 'parada', 'visit', 'visita', 'go to', 'ir a', 'break', 'descanso',
            'detour', 'desvío', 'quick stop', 'brief stop', 'pickup', 'drop off',
            'swing by', 'pass by', 'pasar por', 'hacer parada', 'detenerse'
        ];

        // Palabras que indican tiendas/mercados (incluyendo variaciones)
        const storeWords = [
            // Términos generales
            'store', 'market', 'supermarket', 'super', 'grocery', 'shop', 'shopping',
            'tienda', 'mercado', 'supermercado', 'compras', 'comprar', 'comercial', 'plaza',
            'mall', 'centro comercial', 'shopping center', 'retail',

            // Cadenas específicas de México
            'walmart', 'wal-mart', 'costco', 'sams', "sam's club", 'soriana', 'chedraui',
            'comercial mexicana', 'mega', 'superama', 'bodega aurrera', 'aurrera',
            'heb', 'city club', 'la comer', 'sumesa', 'casa ley', 'smart',
            'liverpool', 'palacio de hierro', 'sears', 'suburbia', 'coppel',
            'elektra', 'famsa', 'office depot', 'office max', 'best buy',

            // Farmacias y servicios en México
            'pharmacy', 'farmacia', 'farmacias del ahorro', 'farmacia guadalajara',
            'farmacia benavides', 'farmacia san pablo', 'similares', 'yza',
            'gas station', 'gasolinera', 'pemex', 'bp', 'shell', 'mobil',
            'convenience store', 'tienda de conveniencia', 'oxxo', '7-eleven', 'extra',

            // Términos en español
            'autoservicio', 'minisuper', 'abarrotes', 'despensa', 'bodega',
            'centro de compras', 'almacén', 'hipermercado'
        ];

        // Palabras que indican tiempo
        const timeWords = [
            'minute', 'min', 'minuto', 'minutos', 'time', 'tiempo', 'hour', 'hora', 'horas',
            'quick', 'rápido', 'fast', 'brief', 'breve', 'short', 'corto', 'long', 'largo',
            'while', 'rato', 'moment', 'momento', 'second', 'segundo', 'segundos'
        ];

        // Verificar si contiene palabras de parada/visita
        const hasStopWord = stopWords.some(word => lowerText.includes(word));

        // Verificar si contiene palabras de tienda
        const hasStoreWord = storeWords.some(word => lowerText.includes(word));

        // Verificar si contiene palabras de tiempo
        const hasTimeWord = timeWords.some(word => lowerText.includes(word));

        // Detectar patrones problemáticos:
        // 1. Parada + Tienda (ej: "stop at store", "parada en tienda")
        const hasStopAndStore = hasStopWord && hasStoreWord;

        // 2. Tiempo + Tienda (ej: "30 minutes at walmart", "tiempo en mercado")
        const hasTimeAndStore = hasTimeWord && hasStoreWord;

        // 3. Solo mencionar tiendas específicas con contexto de solicitud
        const specificStores = [
            'walmart', 'costco', 'soriana', 'chedraui', 'aurrera', 'bodega aurrera',
            'heb', 'la comer', 'mega', 'superama', 'comercial mexicana', 'sams',
            'oxxo', '7-eleven', 'liverpool', 'palacio de hierro', 'coppel'
        ];

        // Palabras que indican contexto geográfico (permitido)
        const geographicContext = ['desde', 'from', 'de', 'ciudad', 'city', 'estado', 'state', 'país', 'country'];
        const hasGeographicContext = geographicContext.some(word => lowerText.includes(word));

        // Solo bloquear tiendas específicas si NO hay contexto geográfico
        const mentionsSpecificStores = specificStores.some(store => lowerText.includes(store)) && !hasGeographicContext;

        return hasStopAndStore || hasTimeAndStore || mentionsSpecificStores;
    };

    // Validar el texto cada vez que cambie
    useEffect(() => {
        const isInvalid = detectStoreRequest(observations);
        setHasStoreRequest(isInvalid);

        // Notificar al componente padre sobre el estado de validación
        if (onValidationChange) {
            onValidationChange(!isInvalid);
        }
    }, [observations, onValidationChange]);

    const handleTextChange = (e) => {
        handleChange(e);
    };

    return (
        <div className="grid-x grid-padding-x ">
            <div className="medium-12 cell">
                <h4>
                    <FontAwesomeIcon icon="pen-alt" />
                    Observations:
                </h4>
                <p>Do you have any observations or special requests?</p>

                {/* Mensaje de advertencia si se detecta solicitud de parada */}
                {hasStoreRequest && (
                    <div className="alert callout warning" style={{ marginBottom: '10px' }}>
                        <p style={{ margin: 0, fontSize: '14px' }}>
                            <FontAwesomeIcon icon="exclamation-triangle" style={{ marginRight: '8px' }} />
                            <strong>Important:</strong> Store/Market stops are a paid service. Please use the "Extra Service" section above to add this service with its corresponding fee.
                        </p>
                    </div>
                )}

                <textarea
                    id="observations"
                    value={observations}
                    name="observations"
                    onChange={handleTextChange}
                    type="text"
                    placeholder="type observations (store stops are not allowed here - use Extra Service section)"
                    rows="4"
                    style={{
                        borderColor: hasStoreRequest ? '#cc4b37' : '',
                        borderWidth: hasStoreRequest ? '2px' : ''
                    }}>
                </textarea>

                {hasStoreRequest && (
                    <small style={{ color: '#cc4b37', fontSize: '12px', marginTop: '5px', display: 'block' }}>
                        Please remove store/market stop requests from observations and use the Extra Service section instead.
                    </small>
                )}
            </div>
        </div>
    );
};

export default ObservationsSection;
